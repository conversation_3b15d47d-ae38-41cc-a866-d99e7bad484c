<!-- trauma-game.hmtl -->
<!DOCTYPE html>
<html lang="de">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />

  <link rel="icon" type="image/png" href="/profilbilder/favicon/index/favicon-96x96.png" sizes="96x96" />
  <link rel="icon" type="image/svg+xml" href="/profilbilder/favicon/index/favicon.svg" />
  <link rel="shortcut icon" href="/profilbilder/favicon/index/favicon.ico" />
  <link rel="apple-touch-icon" sizes="180x180" href="/profilbilder/favicon/index/apple-touch-icon.png" />
  <meta name="apple-mobile-web-app-title" content="0nefinity.love" />
  <link rel="manifest" href="/profilbilder/favicon/index/site.webmanifest" />
  
  <link href="/meta.css" rel="stylesheet" /><!-- meta.css regelt Hintergrund- und Objekt-Farben etc. (Bitte diese Zeile hier unangetastet lassen) -->
  <script src="/meta.js" defer></script><!-- meta.js regelt das Menü und sonstige Funktionen (Bitte diese Zeile hier unangetastet lassen)-->
  
  <title>Trauma-Game</title>
  <style>
    html, body {
      margin: 0;
      padding: 0;
    }
    #gameContainer {
      position: relative;
      width: 100vw;
      height: 100vh;
      overflow: hidden;
    }
    #traumaCloud {
      position: absolute;
      width: 20vmin;
      height: 20vmin;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      border: 2px dashed var(--text-color);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--text-color);
      pointer-events: none;
    }
    #solutionRing {
      position: absolute;
      width: 30vmin;
      height: 30vmin;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      border-radius: 50%;
      background: var(--bg-color);

      display: flex;
      align-items: center;
      justify-content: center;
    }
    #hole {
      position: absolute;
      width: 24vmin;
      height: 24vmin;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      background: var(--bg-color);
      border-radius: 50%;
      pointer-events: none;
    }
    #solutionText {
      position: absolute;
      width: 100%;
      text-align: center;
      font-weight: bold;
      color: var(--text-color);
      pointer-events: none;
      font-size: 1.2em;
    }
    #problemCircle {
      position: absolute;
      width: 26vmin;
      height: 26vmin;
      border-radius: 50%;
      background: var(--text-color);
      color: var(--bg-color);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      user-select: none;
      touch-action: none;
      font-size: 1.2em;
    }
    #problemCircle span {
      font-weight: bold;
    }
  </style>
</head>
<body>
  <div id="gameContainer">
    <div id="traumaCloud">Trauma</div>
    <div id="solutionRing">
      <div id="solutionText">Solution</div>
      <div id="hole"></div>
    </div>
    <div id="problemCircle"><span>Problem</span></div>
  </div>

  <script>
    const gameContainer = document.getElementById('gameContainer');
    const solutionRing = document.getElementById('solutionRing');
    const hole = document.getElementById('hole');
    const problemCircle = document.getElementById('problemCircle');
    let isDragging = false, offsetX, offsetY;
    function getCenter(elem) {
      const rect = elem.getBoundingClientRect();
      return { x: rect.left + rect.width / 2, y: rect.top + rect.height / 2 };
    }
    function setInitialProblemPosition() {
      const solutionCenter = getCenter(solutionRing);
      const holeRadius = hole.offsetWidth / 2;
      const problemRadius = problemCircle.offsetWidth / 2;
      const minDistance = holeRadius + problemRadius + 2;
      const distance = minDistance + 10;
      const angle = 0;
      const problemCenterX = solutionCenter.x + distance * Math.cos(angle);
      const problemCenterY = solutionCenter.y + distance * Math.sin(angle);
      const containerRect = gameContainer.getBoundingClientRect();
      const problemRect = problemCircle.getBoundingClientRect();
      problemCircle.style.left = (problemCenterX - containerRect.left - problemRect.width / 2) + "px";
      problemCircle.style.top  = (problemCenterY - containerRect.top  - problemRect.height / 2) + "px";
    }
    window.addEventListener('load', setInitialProblemPosition);
    window.addEventListener('resize', setInitialProblemPosition);
    problemCircle.addEventListener('pointerdown', (e) => {
      isDragging = true;
      const rect = problemCircle.getBoundingClientRect();
      offsetX = e.clientX - rect.left;
      offsetY = e.clientY - rect.top;
      problemCircle.style.transition = "none";
    });
    document.addEventListener('pointermove', (e) => {
      if (!isDragging) return;
      const containerRect = gameContainer.getBoundingClientRect();
      let newLeft = e.clientX - containerRect.left - offsetX;
      let newTop  = e.clientY - containerRect.top  - offsetY;
      problemCircle.style.left = newLeft + "px";
      problemCircle.style.top  = newTop + "px";
      const solutionCenter = getCenter(solutionRing);
      const problemRect = problemCircle.getBoundingClientRect();
      const problemCenter = { x: problemRect.left + problemRect.width / 2, y: problemRect.top + problemRect.height / 2 };
      const holeRadius = hole.offsetWidth / 2;
      const problemRadius = problemCircle.offsetWidth / 2;
      const minDistance = holeRadius + problemRadius + 2;
      const dx = problemCenter.x - solutionCenter.x;
      const dy = problemCenter.y - solutionCenter.y;
      const dist = Math.sqrt(dx * dx + dy * dy);
      if (dist < minDistance) {
        const push = minDistance - dist;
        const pushX = (dx / dist) * push;
        const pushY = (dy / dist) * push;
        newLeft += pushX;
        newTop  += pushY;
        problemCircle.style.left = newLeft + "px";
        problemCircle.style.top  = newTop  + "px";
      }
    });
    document.addEventListener('pointerup', () => {
      if (!isDragging) return;
      isDragging = false;
      const solutionCenter = getCenter(solutionRing);
      const problemRect = problemCircle.getBoundingClientRect();
      const problemCenter = { x: problemRect.left + problemRect.width / 2, y: problemRect.top + problemRect.height / 2 };
      const dx = problemCenter.x - solutionCenter.x;
      const dy = problemCenter.y - solutionCenter.y;
      const dist = Math.sqrt(dx * dx + dy * dy);
      const holeRadius = hole.offsetWidth / 2;
      if (dist < holeRadius + 5) {
        const containerRect = gameContainer.getBoundingClientRect();
        const targetX = solutionCenter.x - containerRect.left - problemRect.width / 2;
        const targetY = solutionCenter.y - containerRect.top - problemRect.height / 2;
        problemCircle.style.transition = "left 0.5s, top 0.5s";
        problemCircle.style.left = targetX + "px";
        problemCircle.style.top  = targetY + "px";
        setTimeout(() => { problemCircle.style.transition = ""; }, 600);
      }
    });
  </script>
</body>
</html>
