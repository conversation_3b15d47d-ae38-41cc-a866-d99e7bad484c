:root {
  --bg-color: black;
  --text-color: white;
  --accent-color: #4a90e2;
  --toolbar-bg: rgba(30, 30, 30, 0.8);
  --panel-bg: rgba(40, 40, 40, 0.9);
  --border-color: rgba(255, 255, 255, 0.2);
  --hover-color: rgba(255, 255, 255, 0.1);
  --active-color: rgba(74, 144, 226, 0.6);
  --shadow-color: rgba(0, 0, 0, 0.3);
  --grid-color: rgba(255, 255, 255, 0.1);
  --axis-color: rgba(255, 255, 255, 0.3);
}

:root.light-theme {
  --bg-color: white;
  --text-color: black;
  --toolbar-bg: rgba(240, 240, 240, 0.8);
  --panel-bg: rgba(245, 245, 245, 0.9);
  --border-color: rgba(0, 0, 0, 0.2);
  --hover-color: rgba(0, 0, 0, 0.05);
  --active-color: rgba(74, 144, 226, 0.3);
  --shadow-color: rgba(0, 0, 0, 0.1);
  --grid-color: rgba(0, 0, 0, 0.1);
  --axis-color: rgba(0, 0, 0, 0.3);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Verdana, Geneva, Tahoma, sans-serif;
  background-color: var(--bg-color);
  color: var(--text-color);
  overflow: hidden;
  height: 100vh;
  width: 100vw;
  transition: background-color 0.3s ease;
}

#canvas-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

#infinite-canvas {
  position: absolute;
  top: 0;
  left: 0;
  cursor: grab;
}

#infinite-canvas:active {
  cursor: grabbing;
}

#toolbar {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  background-color: var(--toolbar-bg);
  padding: 10px;
  border-radius: 8px;
  z-index: 100;
  box-shadow: 0 2px 10px var(--shadow-color);
  transition: background-color 0.3s ease;
}

.tool-group {
  display: flex;
  gap: 5px;
  border-right: 1px solid var(--border-color);
  padding-right: 10px;
  margin-right: 10px;
}

.tool-group:last-child {
  border-right: none;
  padding-right: 0;
  margin-right: 0;
}

.tool-button {
  background-color: transparent;
  color: var(--text-color);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 5px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.tool-button:hover {
  background-color: var(--hover-color);
}

.tool-button.active {
  background-color: var(--active-color);
  border-color: var(--accent-color);
}

.tool-button svg {
  width: 24px;
  height: 24px;
}

#properties-panel,
#circle-properties,
#formula-properties {
  position: fixed;
  right: 20px;
  top: 20px;
  background-color: var(--panel-bg);
  padding: 15px;
  border-radius: 8px;
  width: 250px;
  z-index: 100;
  box-shadow: 0 2px 10px var(--shadow-color);
  transition: background-color 0.3s ease;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 5px;
}

.panel-header h3 {
  font-size: 16px;
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  color: var(--text-color);
  font-size: 20px;
  cursor: pointer;
  padding: 0 5px;
}

.property {
  margin-bottom: 12px;
}

.property label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
}

.property input, 
.property select {
  width: 100%;
  padding: 5px;
  background-color: rgba(0, 0, 0, 0.3);
  border: 1px solid var(--border-color);
  color: var(--text-color);
  border-radius: 4px;
}

.property input[type="range"] {
  display: inline-block;
  width: 70%;
  vertical-align: middle;
}

#font-size-value,
#radius-value,
#border-value,
#formula-size-value {
  display: inline-block;
  width: 25%;
  text-align: right;
  font-size: 14px;
}

.button-group {
  display: flex;
  gap: 5px;
}

.align-button {
  flex: 1;
  background-color: transparent;
  color: var(--text-color);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 5px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.align-button:hover {
  background-color: var(--hover-color);
}

.align-button.active {
  background-color: var(--active-color);
  border-color: var(--accent-color);
}

#minimap-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 150px;
  height: 150px;
  background-color: var(--panel-bg);
  border-radius: 8px;
  overflow: hidden;
  z-index: 100;
  box-shadow: 0 2px 10px var(--shadow-color);
  transition: background-color 0.3s ease;
}

#minimap {
  width: 100%;
  height: 100%;
}

#info-panel {
  position: fixed;
  bottom: 20px;
  left: 20px;
  background-color: var(--panel-bg);
  padding: 8px 12px;
  border-radius: 8px;
  z-index: 100;
  box-shadow: 0 2px 10px var(--shadow-color);
  transition: background-color 0.3s ease;
  font-size: 12px;
}

.coordinates {
  display: flex;
  gap: 15px;
}

.hidden {
  display: none;
}

/* For text editing */
.editable-text {
  position: absolute;
  min-width: 50px;
  min-height: 20px;
  padding: 5px;
  outline: none;
  white-space: pre-wrap;
  cursor: text;
  z-index: 50;
  background-color: transparent;
  border: 1px dashed transparent;
}

.editable-text:focus {
  border-color: var(--accent-color);
  background-color: rgba(0, 0, 0, 0.3);
}

/* Grid styling */
.grid-line {
  stroke: var(--grid-color);
  stroke-width: 1;
}

.grid-axis {
  stroke: var(--axis-color);
  stroke-width: 2;
}

/* Tooltip */
.tooltip {
  position: absolute;
  background-color: var(--panel-bg);
  color: var(--text-color);
  padding: 5px 8px;
  border-radius: 4px;
  font-size: 12px;
  pointer-events: none;
  z-index: 1000;
  box-shadow: 0 2px 5px var(--shadow-color);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.tooltip.visible {
  opacity: 1;
}

/* Context menu */
.context-menu {
  position: absolute;
  background-color: var(--panel-bg);
  border-radius: 4px;
  box-shadow: 0 2px 10px var(--shadow-color);
  z-index: 1000;
  min-width: 150px;
  overflow: hidden;
}

.context-menu-item {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.context-menu-item:hover {
  background-color: var(--hover-color);
}

.context-menu-separator {
  height: 1px;
  background-color: var(--border-color);
  margin: 5px 0;
}

/* Loading indicator */
.loading-indicator {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  z-index: 1000;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: var(--accent-color);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  #toolbar {
    flex-wrap: wrap;
    width: 90%;
    max-width: 400px;
  }
  
  .tool-group {
    border-right: none;
    border-bottom: 1px solid var(--border-color);
    padding-right: 0;
    margin-right: 0;
    padding-bottom: 5px;
    margin-bottom: 5px;
    width: 100%;
    justify-content: center;
  }
  
  .tool-group:last-child {
    border-bottom: none;
    padding-bottom: 0;
    margin-bottom: 0;
  }
  
  #properties-panel,
  #circle-properties,
  #formula-properties {
    width: 90%;
    max-width: 300px;
    right: 50%;
    transform: translateX(50%);
  }
  
  #minimap-container {
    width: 100px;
    height: 100px;
  }
}
