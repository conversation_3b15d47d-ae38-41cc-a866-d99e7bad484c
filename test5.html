<!DOCTYPE html>
<html lang="de">
<head>
  <meta charset="UTF-8">
  <title>Test field-sizing</title>
  <style>
    input, select, textarea {
      field-sizing: content;
      /* Optional: andere Eigenschaften, um zu sehen, ob die erkannt werden */
      background-color: lightyellow;
    }
  </style>
</head>
<body>
  <h1>Test: field-sizing: content;</h1>
  <input type="text" placeholder="Testeingabe" />
  <textarea placeholder="Testeingabe"></textarea>

  <script>
    // Kurzer Check per JavaScript:
    console.log("'field-sizing' in document.body.style?", 'field-sizing' in document.body.style);
  </script>
</body>
</html>
