<!-- von-<PERSON><PERSON>-und-<PERSON><PERSON>.html -->
<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/png" href="/profilbilder/favicon/index/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/profilbilder/favicon/index/favicon.svg" />
    <link rel="shortcut icon" href="/profilbilder/favicon/index/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/profilbilder/favicon/index/apple-touch-icon.png" />
    <meta name="apple-mobile-web-app-title" content="0nefinity.love" />
    <link rel="manifest" href="/profilbilder/favicon/index/site.webmanifest" />
    
    <link href="/meta.css" rel="stylesheet" /><!-- meta.css regelt Hintergrund- und Objekt-Farben etc. (Bitte diese Zeile hier unangetastet lassen) -->
    <script src="/meta.js" defer></script><!-- meta.js regelt das Menü und sonstige Funktionen (Bitte diese Zeile hier unangetastet lassen)-->
  
    <title>von Linien, Punkten, Kreisen und anderen Geschöpfen</title>
</head>
<body>
    <div class="container">
        <p>Eine Linie beliebiger Länge besteht immer aus unendlich vielen Punkten</p>
        <p>Jeder einzelne Abschnitt beliebiger Länge jeder Linie besteht aus unendlich vielen Punkten</p>
        <p>Jede Linie besteht aus unendlich vielen Abschnitten</p>
        <p>Jeder Abschnitt besteht aus unendlich vielen Abschnitten</p>
        <p>Für jede Linie benötigt es nur zwei oder weniger oder mehr Punkte und eine potenziell unendliche Anzahl von Zusatzvariablen um sie zu definieren</p>
        <p>Jeder Punkt ist eine undlich kurze linie in alle Richtungen</p>
        <p>Jeder Punkt ist jede Linie zusammengefaltet</p>
        <p>Jede Linie ist nur ein Punkt mit Zeit</p>
        <p>Jede Linie braucht Raum</p>
        <p>Ein Punkt ist nur eine Linie von oben</p>
        <p>Ein Punkt besteht immer aus unendlich Punkten</p>
        <p>Eine Linie besteht immer aus unendlich Linien</p>
        <p>Hinter den Dingen verbirgt sich die Unendlichkeit, wie ein Punkt der von der Seite betrachtet schon immer eine Linie war</p>
        <p>Jeder Punkt könnte unendlichfach übereinander liegen, ohne dass es bemerkbar wäre</p>
        <p>Jede Linie lässt sich so beugen, dass sie Punktförmig erscheint</p>
        <p>Eine Linie könnte man beliebig verlängern</p>
        <p>Linie und Punkt sind ohne stroke-width unsichtbar, sie sind fast kleiner als existent</p>
        <p>Eine Linie ohne Punkte wäre nichts</p>
        <p>Ein Punkt könnte Teil jeder Linie sein</p>
        <p>Manche Linien verzweigen sich</p>
        <p>Manche Linien treffen sich selbst und bilden geometrische Formen</p>
        <p>Eine Linie, die im Kreis rennt bildet, nun ja, einen Kreis. Wie häufig sie drum herum rennt ist dabei irrelevant. Wenn sie 0 mal rennt, dann haben wir keinen Kreis. Dann haben wir nur ein rundes Nichts, wenn überhaupt. Wenn sie unendlich mal rum rennt, dann haben wir einen sogenannten doppelt unendlichen Kreis.</p>
        <p>Ein Punkt ist Raumlos. Er hat im innen so wenig Raum wie im außen. 
            In diesem Spannungsfeld explodiert/implodiert der Raum nach innen/außen.
            In den Grenzschichten bilden sich Universen.</p>

        <p>Ein Kreis impliziert alle Dreiecke die er gleichzeitig direkt und indirekt enthält und auch alle die er nicht enthält.</p>
       
    </div>
</body>
</html>