//randomness.js


/*<PERSON><PERSON>, die randomness kreiert welche bei Bedarf abgerufen werden kann.

Sie soll als Einstellungsseite veränderbar sein und ein Veränderungsicon ins Menü einbauen.

Sie soll schlank sein 
Jegliche dinge sollen damit randomisiert werden können wie Farbwerte, Positionswerte, Zahlenwerte, Zeitdauer bis irgendwas umswitcht, reihenfolgen, jeg<PERSON><PERSON> zeug.


Der Randomness Faktor soll dynamisch eingestellt werden können, sodass man ihn bei bedarf erhöhen oder absenken kann.
Evtl eine klasse?

Es sollen Daten einfließen wie Screengröße, relativer Zeitpunkt aus human Sicht, Ort(Geolocation auf der Erde (frei auswählbar), Position der Erde innerhalb der Milchstraße, innerhalb des keine Ahnung wie man die größeren Astrologischen Dimensionen nennt, aber sie sollen alle einfließen, damit Alien<PERSON> auch noch eine unterschiedliche Sicht angezeigt kriegen würden). Irgendwelche mathematischen Funktionen und irgendwelche Formeln aus der Physik, Quantenmechanik, Relativität, Anzahl Jahre, die das Individuum glaubt alt zu sein (Hier soll man eine Beliebige Zahl eingeben können oder "ewig" einstellen können.)

Diese ganzen Faktoren sollen jeweils zudem Zufällig miteinander kombiniert werden.

Man soll auch Einstellmöglichkeiten treffen können.

Sodass die Seite prinzipiell immer leicht unterschiedlich aussieht, je nachdem wer von wo sie wie Anschaut. 

Es soll diese ganze randomness aus den unendlich quellen zusammengerechnet werden und daraus soll sich die randomness der einzelnen Aspekte ergeben. Sodass eine große universelle randomness entsteht, auf der alles basieren soll
