<!DOCTYPE html>
<html lang="de">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  
  <link href="/meta.css" rel="stylesheet" /><!-- meta.css regelt Hintergrund- und Objekt-Farben etc. benutze (Bitte diese Zeile hier unangetastet lassen) -->
  <script src="/meta.js" defer></script><!-- meta.js regelt das Menü und sonstige Funktionen (Bitte diese Zeile hier unangetastet lassen) -->
  
  <title>Bibeltext-Visualisierung</title>
  <style>
    body {
      margin: 0;
      overflow: hidden;
    }
    canvas {
      display: block;
    }
  </style>
</head>
<body>
  <canvas id="canvas"></canvas>
  
  <script>
    const canvas = document.getElementById('canvas');
    const ctx = canvas.getContext('2d');

    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    let textData = '';
    let characterCount = 0;
    let lines = [];
    let radius = Math.min(canvas.width, canvas.height) / 2 - 50;
    let fontSize = 12;
    let minFontSize = 0.1; // untere Grenze
    let isDragging = false;
    let lastX = 0;
    let lastY = 0;
    let panX = 0;
    let panY = 0;
    let scale = 1;
    let renderRequested = false;

    // Bibeltext laden
    fetch('bibel.txt')
      .then(response => response.text())
      .then(data => {
        // Text normalisieren: Zeilenumbrüche und überflüssige Leerzeichen entfernen
        textData = data.replace(/\r\n/g, ' ').replace(/\n/g, ' ').replace(/\s+/g, ' ').trim();
        characterCount = textData.length;
        console.log(`Loaded text with ${characterCount} characters`);
        
        init();
      })
      .catch(error => console.error('Error loading text:', error));

    // Berechnet, wie viele Zeichen bei gegebener Schriftgröße in den Kreis passen
    function calculateCapacity(fontSize) {
      ctx.font = `${fontSize}px monospace`;
      const charWidth = ctx.measureText('M').width;
      const lineHeight = fontSize * 1.2;
      let capacity = 0;
      
      // Schleife über die Zeilen, die in den Kreis passen
      for (let y = -radius + lineHeight / 2; y <= radius - lineHeight / 2; y += lineHeight) {
        // Berechne die Zeilenbreite an der y-Position (Kreisgleichung)
        const lineWidth = 2 * Math.sqrt(Math.max(0, radius * radius - y * y));
        const charsPerLine = Math.floor(lineWidth / charWidth);
        if (charsPerLine > 0) {
          capacity += charsPerLine;
        }
      }
      
      return capacity;
    }

    // Optimale Schriftgröße per Binärsuche ermitteln (ohne Sicherheitsfaktor)
    function findOptimalFontSize() {
      const L = textData.length;
      let minSize = 0.1;   // untere Schranke
      let maxSize = 200;   // obere Schranke (groß genug wählen)
      let bestSize = minSize;
      
      for (let i = 0; i < 50; i++) { // 50 Iterationen für hohe Genauigkeit
        const mid = (minSize + maxSize) / 2;
        const capacity = calculateCapacity(mid);
        
        if (capacity >= L) {
          // Alles passt, also versuchen wir, noch etwas größer zu gehen
          bestSize = mid;
          minSize = mid;
        } else {
          // Es passt nicht, also verkleinern
          maxSize = mid;
        }
      }
      
      return bestSize;
    }

    // Erzeugt die Zeilen (mit rundem Layout) basierend auf der ermittelten Schriftgröße
    function generateLines() {
      const lineHeight = fontSize * 1.2;
      const charWidth = ctx.measureText('M').width;
      
      lines = [];
      let textIndex = 0;
      
      for (let y = -radius + lineHeight / 2; y <= radius - lineHeight / 2; y += lineHeight) {
        const lineWidth = 2 * Math.sqrt(Math.max(0, radius * radius - y * y));
        const charsPerLine = Math.floor(lineWidth / charWidth);
        
        if (charsPerLine <= 0) continue;
        
        const remainingChars = textData.length - textIndex;
        const charsInLine = Math.min(charsPerLine, remainingChars);
        const lineText = textData.substr(textIndex, charsInLine);
        textIndex += charsInLine;
        
        if (lineText.length > 0) {
          lines.push({
            text: lineText,
            y: y,
            width: lineWidth
          });
        }
        
        if (textIndex >= textData.length) break;
      }
      
      if (textIndex < textData.length) {
        console.warn(`Not all text fits: ${textIndex}/${textData.length} characters used (${(textIndex/textData.length*100).toFixed(2)}%).`);
      } else {
        console.log(`All text fits: ${textIndex}/${textData.length} characters used (100%).`);
      }
    }

    function requestRender() {
      if (!renderRequested) {
        renderRequested = true;
        requestAnimationFrame(render);
      }
    }

    function render() {
      renderRequested = false;
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      ctx.save();
      
      // Textfarbe aus CSS-Variable beziehen (Fallback: weiß)
      const textColor = getComputedStyle(document.body).getPropertyValue('--text-color').trim() || 'white';
      ctx.fillStyle = textColor;
      
      // Transformationen: Mittelpunkt + Pan und Zoom
      ctx.translate(canvas.width / 2 + panX, canvas.height / 2 + panY);
      ctx.scale(scale, scale);
      
      ctx.font = `${fontSize}px monospace`;
      ctx.textBaseline = 'middle';
      ctx.textAlign = 'center';
      
      const effectiveFontSize = fontSize * scale;
      
      if (effectiveFontSize < 0.4) {
        renderPixelAveraged();
      } else {
        renderText();
      }
      
      ctx.restore();
    }
    
    // Rendert normalen Text (nur sichtbare Zeilen)
    function renderText() {
      const visibleLeft = (-panX / scale) - (canvas.width / 2 / scale);
      const visibleRight = (-panX / scale) + (canvas.width / 2 / scale);
      const visibleTop = (-panY / scale) - (canvas.height / 2 / scale);
      const visibleBottom = (-panY / scale) + (canvas.height / 2 / scale);
      
      for (const line of lines) {
        if (line.y < visibleTop - fontSize || line.y > visibleBottom + fontSize) {
          continue;
        }
        
        ctx.fillText(line.text, 0, line.y);
      }
    }
    
    // Berechnet die Textdichte in einem bestimmten Bereich
    function calculateTextDensity(text, position, range) {
      const start = Math.max(0, position - range);
      const end = Math.min(text.length, position + range);
      const subset = text.substring(start, end);
      const nonSpaceCount = subset.replace(/\s/g, '').length;
      return nonSpaceCount / (end - start);
    }
    
    // Pixel-Averaging-Rendering, falls Text zu klein ist
    function renderPixelAveraged() {
      const pixelSize = 1 / scale;
      const step = Math.max(1, Math.floor(pixelSize));
      
      const visibleLeft = (-panX / scale) - (canvas.width / 2 / scale);
      const visibleRight = (-panX / scale) + (canvas.width / 2 / scale);
      const visibleTop = (-panY / scale) - (canvas.height / 2 / scale);
      const visibleBottom = (-panY / scale) + (canvas.height / 2 / scale);
      
      for (let y = Math.max(-radius, visibleTop); y <= Math.min(radius, visibleBottom); y += step) {
        for (let x = Math.max(-radius, visibleLeft); x <= Math.min(radius, visibleRight); x += step) {
          if (x * x + y * y <= radius * radius) {
            const lineIndex = Math.floor((y + radius) / (fontSize * 1.2));
            if (lineIndex >= 0 && lineIndex < lines.length) {
              const line = lines[lineIndex];
              if (Math.abs(x) <= line.width / 2) {
                const charWidth = ctx.measureText('M').width;
                const charPosition = Math.floor((x + line.width / 2) / charWidth);
                
                if (charPosition >= 0 && charPosition < line.text.length) {
                  const density = calculateTextDensity(line.text, charPosition, 5);
                  ctx.globalAlpha = 0.2 + (density * 0.8);
                  ctx.fillRect(x - step / 2, y - step / 2, step, step);
                } else {
                  ctx.globalAlpha = 0.2;
                  ctx.fillRect(x - step / 2, y - step / 2, step, step);
                }
              }
            }
          }
        }
      }
      
      ctx.globalAlpha = 1.0;
    }

    // Event-Handler für Drag & Zoom
    canvas.addEventListener('mousedown', (e) => {
      isDragging = true;
      lastX = e.clientX;
      lastY = e.clientY;
    });

    canvas.addEventListener('mousemove', (e) => {
      if (isDragging) {
        const dx = e.clientX - lastX;
        const dy = e.clientY - lastY;
        panX += dx;
        panY += dy;
        lastX = e.clientX;
        lastY = e.clientY;
        requestRender();
      }
    });

    canvas.addEventListener('mouseup', () => {
      isDragging = false;
    });

    canvas.addEventListener('mouseleave', () => {
      isDragging = false;
    });

    canvas.addEventListener('wheel', (e) => {
      e.preventDefault();
      
      const mouseX = e.clientX - canvas.width / 2 - panX;
      const mouseY = e.clientY - canvas.height / 2 - panY;
      
      const zoomIntensity = 0.001;
      const deltaScale = 1 + e.deltaY * -zoomIntensity;
      const newScale = Math.min(Math.max(0.01, scale * deltaScale), 100000);
      
      if (newScale !== scale) {
        const scaleRatio = newScale / scale;
        panX -= mouseX * (scaleRatio - 1);
        panY -= mouseY * (scaleRatio - 1);
        scale = newScale;
        requestRender();
      }
    });

    window.addEventListener('resize', () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
      radius = Math.min(canvas.width, canvas.height) / 2 - 50;
      init();
    });

    // Initialisierung: optimale Schriftgröße berechnen, Zeilen generieren, Render anstoßen
    function init() {
      fontSize = findOptimalFontSize();
      console.log("Gefundene Schriftgröße:", fontSize);
      generateLines();
      requestRender();
    }
  </script>
</body>
</html>
