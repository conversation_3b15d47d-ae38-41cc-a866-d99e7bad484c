<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/png" href="/profilbilder/favicon/index/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/profilbilder/favicon/index/favicon.svg" />
    <link rel="shortcut icon" href="/profilbilder/favicon/index/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/profilbilder/favicon/index/apple-touch-icon.png" />
    <meta name="apple-mobile-web-app-title" content="0nefinity.love" />
    <link rel="manifest" href="/profilbilder/favicon/index/site.webmanifest" />
    
    <link href="/meta.css" rel="stylesheet" /><!-- meta.css regelt Hintergrund- und Objekt-Farben etc. (Bitte diese Zeile hier unangetastet lassen) -->
    <script src="/meta.js" defer></script><!-- meta.js regelt das Menü und sonstige Funktionen (Bitte diese Zeile hier unangetastet lassen)-->
  
    <meta name="description" content="0 1 ∞ are the Scutum Fidei of math" />
    <meta name="keywords" content="0nefinity, nondual math, 0 1 ∞ Scrutum Fidei of math" />
  
  <title>Scutum Fidei</title>
</head>
<body>

<svg width="400" height="400" viewBox="0 0 400 400">
  <!-- Hintergrund -->
  <rect x="0" y="0" width="100%" height="100%" fill="#fafafa" />

  <!-- Positionen der Kreise (vereinfacht) -->
  <!-- Oben (Vater) -->
  <circle cx="200" cy="60" r="30" fill="#ddd" stroke="#000" stroke-width="2"/>
  <text x="200" y="60" text-anchor="middle" dominant-baseline="central">
    Vater
  </text>

  <!-- Links unten (Sohn) -->
  <circle cx="80" cy="300" r="30" fill="#ddd" stroke="#000" stroke-width="2"/>
  <text x="80" y="300" text-anchor="middle" dominant-baseline="central">
    Sohn
  </text>

  <!-- Rechts unten (Heiliger Geist) -->
  <circle cx="320" cy="300" r="30" fill="#ddd" stroke="#000" stroke-width="2"/>
  <text x="320" y="300" text-anchor="middle" dominant-baseline="central">
    Heiliger<br/>Geist
  </text>

  <!-- Zentrum (Gott) -->
  <circle cx="200" cy="180" r="30" fill="#ddd" stroke="#000" stroke-width="2"/>
  <text x="200" y="180" text-anchor="middle" dominant-baseline="central">
    Gott
  </text>

  <!-- Linien zum Zentrum (alle "ist") -->
  <line x1="200" y1="90" x2="200" y2="150" stroke="#000" stroke-width="2" />
  <text x="200" y="120" text-anchor="middle" dominant-baseline="central">
    ist
  </text>

  <line x1="110" y1="280" x2="185" y2="210" stroke="#000" stroke-width="2" />
  <text x="150" y="245" text-anchor="middle" dominant-baseline="central">
    ist
  </text>

  <line x1="290" y1="280" x2="215" y2="210" stroke="#000" stroke-width="2" />
  <text x="250" y="245" text-anchor="middle" dominant-baseline="central">
    ist
  </text>

  <!-- Äußeres Dreieck (alle "ist nicht") -->
  <line x1="200" y1="60" x2="80" y2="300" stroke="#000" stroke-width="2" />
  <text x="140" y="170" transform="rotate(63, 140, 170)" text-anchor="middle">
    ist nicht
  </text>

  <line x1="80" y1="300" x2="320" y2="300" stroke="#000" stroke-width="2" />
  <text x="200" y="303" text-anchor="middle" dominant-baseline="hanging">
    ist nicht
  </text>

  <line x1="320" y1="300" x2="200" y2="60" stroke="#000" stroke-width="2" />
  <text x="260" y="170" transform="rotate(-63, 260, 170)" text-anchor="middle">
    ist nicht
  </text>

</svg>

</body>
</html>
