<!DOCTYPE html>
<html lang="de">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>invent your own mathematical system (or whatever you want)</title>
  <link href="/meta.css" rel="stylesheet"/>
  <script src="/meta.js" defer></script>
  <style>
    html, body {
      margin: 0;
      padding: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }
    #canvas {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 0;
      touch-action: none;
    }
    .topOverlay {
      position: relative;
      z-index: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      user-select: none;
      pointer-events: none;
    }
    .topOverlay button,
    .topOverlay input,
    .topOverlay label {
      pointer-events: auto;
    }
    .header-container {
      text-align: center;
      margin-bottom: 10px;
      padding: 10px 70px;
    }
    .header-container h3 {
      margin: 0;
      font-size: clamp(1.5rem, 5vw, 2.5rem);
    }
    .header-container div {
      margin: 0;
      font-size: clamp(1rem, 4vw, 1.8rem);
    }
    .toolbar {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      gap: 10px;
      margin: 0;
      padding: 0;
    }
    .toolbar-group {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      margin: 0;
      padding: 0;
    }
    .pen-settings input,
    .pen-settings button,
    .toolbar-button,
    .brush-button {
      font-size: 1em;
      background: transparent;
      border: 1px solid var(--text-color, #000);
      color: var(--text-color, #000);
      padding: 0.5em;
      border-radius: 4px;
      user-select: none;
      height: 2.5em;
      box-sizing: border-box;
      text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
      line-height: 1;
      cursor: pointer;
    }
    .color-picker {
      width: 2.5em;
      min-width: 2.5em;
      cursor: pointer;
      padding: 0;
    }
    .brush-button {
      width: 40px;
      text-align: center;
      transition: background-color 0.2s, transform 0.1s;
    }
    .brush-button:hover {
      opacity: 0.9;
    }
    .brush-button:active {
      transform: scale(0.95);
    }
    .brush-size-container {
      display: inline-flex;
      align-items: center;
      margin: 0;
    }
    .brush-size-container > * {
      margin: 0 !important;
      border-radius: 0;
    }
    .brush-size-container > button:first-child {
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
    }
    .brush-size-container > input[type="number"] {
      width: 70px;
      text-align: center;
      padding: 0.5em;
      border-left: none;
      border-right: none;
    }
    .brush-size-container > button:last-child {
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
    }
  </style>
</head>
<body>
  <canvas id="canvas"></canvas>
  <div class="topOverlay" id="topOverlay">
    <div class="header-container">
      <h3>invent your own mathematical system</h3>
      <div>(or whatever you want)</div>
    </div>
    <div class="toolbar">
      <div class="toolbar-group">
        <div id="penSettings" class="pen-settings">
          <input type="color" id="colorPicker" class="color-picker" title="Farbe wählen">
          <div class="brush-size-container">
            <button id="decreaseBtn" class="brush-button" title="Pinselgröße verringern">-</button>
            <input type="number" id="brushSize" class="number-picker" min="0" value="1" step="any" title="Pinselgröße">
            <button id="increaseBtn" class="brush-button" title="Pinselgröße erhöhen">+</button>
          </div>
        </div>
        <button id="clearBtn" class="toolbar-button" title="Leinwand löschen">another one</button>
      </div>
    </div>
  </div>
  <script>
    const canvas = document.getElementById('canvas');
    const ctx = canvas.getContext('2d');
    const colorPicker = document.getElementById('colorPicker');
    const brushSize = document.getElementById('brushSize');
    const clearBtn = document.getElementById('clearBtn');
    const decreaseBtn = document.getElementById('decreaseBtn');
    const increaseBtn = document.getElementById('increaseBtn');
    const paths = [];
    let drawing = false;
    let lastX = 0, lastY = 0;
    let currentColor = getComputedStyle(document.documentElement)
      .getPropertyValue('--text-color').trim() || '#000000';
    let backgroundColor = getComputedStyle(document.documentElement)
      .getPropertyValue('--bg-color').trim() || '#FFFFFF';
    let currentBrushSize = parseFloat(brushSize.value);
    function resizeCanvas() {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
      renderCanvas();
    }
    window.addEventListener('resize', resizeCanvas);
    window.addEventListener('load', () => {
      resizeCanvas();
      fillCanvasWithBackground();
      renderCanvas();
    });
    function clamp(val, min, max) {
      return Math.max(min, Math.min(val, max));
    }
    function startDrawing(x, y) {
      drawing = true;
      x = clamp(x, 0, canvas.width);
      y = clamp(y, 0, canvas.height);
      lastX = x;
      lastY = y;
      paths.push({ color: currentColor, brushSize: currentBrushSize, points: [{ x, y }] });
    }
    function stopDrawing() {
      drawing = false;
      ctx.beginPath();
    }
    function draw(x, y) {
      if (!drawing) return;
      x = clamp(x, 0, canvas.width);
      y = clamp(y, 0, canvas.height);
      paths[paths.length - 1].points.push({ x, y });
      ctx.strokeStyle = currentColor;
      ctx.lineWidth = currentBrushSize;
      ctx.lineCap = 'round';
      ctx.beginPath();
      ctx.moveTo(lastX, lastY);
      ctx.lineTo(x, y);
      ctx.stroke();
      lastX = x;
      lastY = y;
    }
    function getCanvasCoordinates(clientX, clientY) {
      const rect = canvas.getBoundingClientRect();
      return [clientX - rect.left, clientY - rect.top];
    }
    canvas.addEventListener('pointerdown', e => {
      e.preventDefault();
      const [x, y] = getCanvasCoordinates(e.clientX, e.clientY);
      startDrawing(x, y);
      canvas.setPointerCapture(e.pointerId);
    });
    canvas.addEventListener('pointermove', e => {
      if (!drawing) return;
      e.preventDefault();
      const [x, y] = getCanvasCoordinates(e.clientX, e.clientY);
      draw(x, y);
    });
    canvas.addEventListener('pointerup', e => {
      e.preventDefault();
      stopDrawing();
      canvas.releasePointerCapture(e.pointerId);
    });
    canvas.addEventListener('pointercancel', e => {
      e.preventDefault();
      stopDrawing();
      canvas.releasePointerCapture(e.pointerId);
    });
    colorPicker.value = rgbToHex(currentColor);
    colorPicker.addEventListener('change', e => {
      currentColor = e.target.value;
    });
    brushSize.addEventListener('input', e => {
      let val = parseFloat(e.target.value);
      if (isNaN(val) || val < 0) {
        val = 0;
        brushSize.value = val;
      }
      currentBrushSize = val;
      renderCanvas();
    });
    clearBtn.addEventListener('click', () => {
      fillCanvasWithBackground();
      paths.length = 0;
    });
    decreaseBtn.addEventListener('click', () => {
      if (currentBrushSize === 0) return;
      currentBrushSize /= 10;
      currentBrushSize = Math.round(currentBrushSize * 1e6) / 1e6;
      brushSize.value = (currentBrushSize < 1e-3 && currentBrushSize > 0)
        ? currentBrushSize.toExponential()
        : currentBrushSize;
      renderCanvas();
    });
    increaseBtn.addEventListener('click', () => {
      currentBrushSize *= 10;
      currentBrushSize = Math.round(currentBrushSize * 1e6) / 1e6;
      brushSize.value = (currentBrushSize < 1e-3 && currentBrushSize > 0)
        ? currentBrushSize.toExponential()
        : currentBrushSize;
      renderCanvas();
    });
    function fillCanvasWithBackground() {
      ctx.fillStyle = backgroundColor;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
    }
    function renderCanvas() {
      fillCanvasWithBackground();
      for (const path of paths) {
        if (path.brushSize > 0) {
          ctx.strokeStyle = path.color;
          ctx.lineWidth = path.brushSize;
          ctx.lineCap = 'round';
          ctx.beginPath();
          for (let i = 0; i < path.points.length - 1; i++) {
            const from = path.points[i];
            const to = path.points[i + 1];
            ctx.moveTo(from.x, from.y);
            ctx.lineTo(to.x, to.y);
          }
          ctx.stroke();
        }
      }
    }
    const observer = new MutationObserver(() => {
      currentColor = getComputedStyle(document.documentElement)
        .getPropertyValue('--text-color').trim() || currentColor;
      backgroundColor = getComputedStyle(document.documentElement)
        .getPropertyValue('--bg-color').trim() || backgroundColor;
      if (!colorPicker.value || colorPicker.value === "#000000") {
        colorPicker.value = rgbToHex(currentColor);
      }
      renderCanvas();
    });
    observer.observe(document.documentElement, { attributes: true, attributeFilter: ['style'] });
    function rgbToHex(rgb) {
      const ctx2 = document.createElement("canvas").getContext("2d");
      ctx2.fillStyle = rgb;
      return ctx2.fillStyle;
    }
  </script>
</body>
</html>
