:root {
  --bg-color: black;
  --text-color: white;
  --accent-color: #4a90e2;
  --toolbar-bg: rgba(30, 30, 30, 0.8);
  --panel-bg: rgba(40, 40, 40, 0.9);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Verdana, Geneva, Tahoma, sans-serif;
  background-color: var(--bg-color);
  color: var(--text-color);
  overflow: hidden;
  height: 100vh;
  width: 100vw;
}

#canvas-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

#infinite-canvas {
  position: absolute;
  top: 0;
  left: 0;
  cursor: grab;
}

#infinite-canvas:active {
  cursor: grabbing;
}

#toolbar {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  background-color: var(--toolbar-bg);
  padding: 10px;
  border-radius: 8px;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.tool-group {
  display: flex;
  gap: 5px;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  padding-right: 10px;
  margin-right: 10px;
}

.tool-group:last-child {
  border-right: none;
  padding-right: 0;
  margin-right: 0;
}

.tool-button {
  background-color: transparent;
  color: var(--text-color);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tool-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.tool-button.active {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}

.tool-button[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

#properties-panel {
  position: fixed;
  right: 20px;
  top: 20px;
  background-color: var(--panel-bg);
  padding: 15px;
  border-radius: 8px;
  width: 250px;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

#properties-panel h3 {
  margin-bottom: 15px;
  font-size: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 5px;
}

.property {
  margin-bottom: 12px;
}

.property label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
}

.property input, .property select {
  width: 100%;
  padding: 5px;
  background-color: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--text-color);
  border-radius: 4px;
}

.property input[type="range"] {
  display: inline-block;
  width: 70%;
  vertical-align: middle;
}

#font-size-value {
  display: inline-block;
  width: 25%;
  text-align: right;
  font-size: 14px;
}

#minimap-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 150px;
  height: 150px;
  background-color: var(--panel-bg);
  border-radius: 8px;
  overflow: hidden;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

#minimap {
  width: 100%;
  height: 100%;
}

.hidden {
  display: none;
}

/* For text editing */
.editable-text {
  position: absolute;
  min-width: 50px;
  min-height: 20px;
  padding: 5px;
  outline: none;
  white-space: pre-wrap;
  cursor: text;
}

.editable-text:focus {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Grid styling */
.grid-line {
  stroke: rgba(255, 255, 255, 0.1);
  stroke-width: 1;
}

.grid-axis {
  stroke: rgba(255, 255, 255, 0.3);
  stroke-width: 2;
}
