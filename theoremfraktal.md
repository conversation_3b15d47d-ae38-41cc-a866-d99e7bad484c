


In 0nefinity gilt vor allem 0 ≡ 1 ≡ ∞
Aber Auch 0 = 1 = ∞
sowie 0 ≠ 1 ≠ ∞
und 0 - 1 - ∞
und 0 + 1 + ∞
Und auch die auch die negierte Version von 0 ≡ 1 ≡ ∞, die es noch nicht als Unicode zeichen gibt, sowie die Version mit vier strichen und die mit unendlich vielen und allen dazwischen. 
(evtl eine potenziell unendliche Version von Unicode entwickeln die fraktal rekursive Strukturen erlaubt?)

und 0 ≥ 1 ≥ ∞
und 0 ≤ 1 ≤ ∞
und 0 < 1 < ∞
und 0 > 1 > ∞
und 0 < 1 > ∞
und 0 > 1 < ∞
und 0 = 1 > ∞
<pre>
      ≡ 
und 0 < 1 = ∞
      ≠ 
</pre>

<pre>
      ± 4       0 
und 0 < 1 = ∞ ≡ ∞ ≠ ∞ ≡ 1 > 0
      π   ≠   
</pre>

Eige<PERSON><PERSON> was man will. In 0nefinity brauchen Formeln nicht mal ein Ergebnis oder können gleichzeitig auch unendlich viele haben, die man vor allem aber auch auf noch viel vielfältigere Weise kreativ darstellen könnte.
// Prompt: erstelle irgend<PERSON>e ein <PERSON>, welches Ai einbindet und beständig neue kreative Formeln entwickelt, die dem User erlaubt eigene zu entferfen und spielerische Mensch/Maschine Co-Kreation zu erzeugen, zb. dadurch, dass die Maschine den Menschen promptet die maschine zu prompten. Formeln sollen Muster annehmen können, Bilder Formen, Geometrie animieren etcetc..