# 0nefinity Infinite Canvas

This project transforms the 0nefinity.love website into an infinite, editable canvas that makes the concept of infinity directly experienceable, rather than just a static website.

## Concept

The 0nefinity concept (0 ≡ 1 ≡ ∞) is about the unity of nothingness, singularity, and infinity. This infinite canvas implementation embodies this concept by:

1. Starting from a single point (1)
2. Extending infinitely in all directions (∞)
3. Having the void/nothingness beyond its boundaries (0)

Users can directly interact with and manipulate this infinity, adding their own expressions to the mathematical concept. This makes the abstract concept of infinity directly experienceable rather than just intellectually understood.

## Features

- **Infinite Canvas**: Pan and zoom in any direction without limits
- **Text Editing**: Add and edit text with customizable properties (font, size, color)
- **Shape Creation**: Add circles and other shapes to the canvas
- **Grid System**: Visual reference that adapts to zoom level
- **Minimap**: For orientation in the infinite space
- **Toolbar**: Easy access to tools and controls

## How to Use

1. **Pan**: Click and drag to move around the canvas
2. **Zoom**: Use the mouse wheel or the zoom buttons to zoom in and out
3. **Add Text**: Select the text tool and click anywhere on the canvas
4. **Add Shapes**: Select the shape tool and click on the canvas
5. **Edit Properties**: Use the properties panel to customize text appearance

## Future Enhancements

- More shape types and drawing tools
- Ability to save and load canvas state
- Collaborative editing
- History/undo functionality
- More advanced text formatting options
- Import existing content from 0nefinity.love

## Technical Implementation

The infinite canvas is implemented using HTML5 Canvas with custom pan and zoom functionality. The coordinate system is designed to handle infinite space, with content rendering optimized to only draw visible elements.

---

This project is a test implementation for 0nefinity.love, exploring how to transform the static website into an interactive, infinite canvas that better embodies the concept of 0nefinity.
