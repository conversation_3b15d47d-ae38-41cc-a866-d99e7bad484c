<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Single-Line Vectorizer</title>
  <style>
    #svgOutput {
      border: 1px solid #ccc;
      margin-top: 10px;
    }
  </style>
</head>
<body>
  <h1>Single-Line Vectorizer</h1>
  <input type="file" id="imageInput" accept="image/*">
  <canvas id="canvas" style="display: none;"></canvas>
  <svg id="svgOutput" width="500" height="500"></svg>

  <script>
    // Wandelt ein Bild in Graustufen um
    function toGrayscale(imageData) {
      let data = imageData.data;
      for (let i = 0; i < data.length; i += 4) {
        let r = data[i], g = data[i+1], b = data[i+2];
        let gray = 0.3 * r + 0.59 * g + 0.11 * b;
        data[i] = data[i+1] = data[i+2] = gray;
      }
      return imageData;
    }

    // Wendet einen einfachen Schwellwert an, um ein binäres Bild zu erzeugen
    function threshold(imageData, thresh = 128) {
      let data = imageData.data;
      for (let i = 0; i < data.length; i += 4) {
        let val = data[i] > thresh ? 255 : 0;
        data[i] = data[i+1] = data[i+2] = val;
      }
      return imageData;
    }

    // Ein sehr einfacher (und unvollständiger) Marching Squares Algorithmus:
    // Diese Funktion durchsucht das Bild nach Pixelübergängen und sammelt Punkte.
    function marchingSquares(binaryData, width, height) {
      let contour = [];
      // Hier wird das Bild pixelweise untersucht – dies ist nur ein Demo-Ansatz!
      for (let y = 0; y < height - 1; y++) {
        for (let x = 0; x < width - 1; x++) {
          let i = (y * width + x) * 4;
          let a = binaryData.data[i];
          let b = binaryData.data[i + 4];
          let c = binaryData.data[i + width * 4];
          // Wenn an dieser Stelle ein Übergang erkannt wird, füge einen Punkt hinzu
          if ((a === 0 && b === 255) || (a === 0 && c === 255)) {
            contour.push({ x: x, y: y });
          }
        }
      }
      return contour;
    }

    // Erzeugt einen SVG-Pfad aus einer Liste von Punkten und schließt den Pfad
    function generateSVGPath(points) {
      if (points.length === 0) return "";
      let path = "M " + points[0].x + " " + points[0].y;
      for (let i = 1; i < points.length; i++) {
        path += " L " + points[i].x + " " + points[i].y;
      }
      path += " Z"; // Schließt den Pfad (Endpunkt = Startpunkt)
      return path;
    }

    // Hier können Start- und Endpunkt angepasst werden:
    // Beispiel: Finde den Punkt, der einem von Dir definierten Startpunkt am nächsten kommt,
    // und rotiere das Array so, dass dieser Punkt an erster Stelle steht.
    function adjustStartPoint(points, customStart) {
      if (!points.length) return points;
      // Finde den Index des Punktes, der dem customStart am nächsten ist.
      let minDist = Infinity, startIndex = 0;
      points.forEach((p, i) => {
        let d = Math.hypot(p.x - customStart.x, p.y - customStart.y);
        if (d < minDist) {
          minDist = d;
          startIndex = i;
        }
      });
      // Rotiere das Array, so dass der gefundene Punkt an erster Stelle steht.
      return points.slice(startIndex).concat(points.slice(0, startIndex));
    }

    const imageInput = document.getElementById('imageInput');
    const canvas = document.getElementById('canvas');
    const ctx = canvas.getContext('2d');
    const svgOutput = document.getElementById('svgOutput');

    imageInput.addEventListener('change', function(e) {
      const file = e.target.files[0];
      const img = new Image();
      const reader = new FileReader();
      reader.onload = function(event) {
        img.src = event.target.result;
      }
      img.onload = function() {
        canvas.width = img.width;
        canvas.height = img.height;
        svgOutput.setAttribute("width", img.width);
        svgOutput.setAttribute("height", img.height);
        ctx.drawImage(img, 0, 0);
        let imageData = ctx.getImageData(0, 0, img.width, img.height);
        imageData = toGrayscale(imageData);
        imageData = threshold(imageData, 128);
        ctx.putImageData(imageData, 0, 0);
        let contour = marchingSquares(imageData, img.width, img.height);

        // Optional: Passe den Startpunkt an (z. B. {x: 50, y: 50})
        let customStart = { x: 50, y: 50 };
        contour = adjustStartPoint(contour, customStart);

        let svgPath = generateSVGPath(contour);
        svgOutput.innerHTML = '<path d="' + svgPath + '" stroke="black" fill="none"/>';
      }
      reader.readAsDataURL(file);
    });
  </script>
</body>
</html>
