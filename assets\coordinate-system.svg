<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   width="400"
   height="400"
   viewBox="-50 -50 100 100"
   preserveAspectRatio="xMidYMid meet"
   version="1.1"
   id="svg15"
   sodipodi:docname="coordinate-system.svg"
   inkscape:version="1.4 (86a8ad7, 2024-10-11)"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:svg="http://www.w3.org/2000/svg">
  <sodipodi:namedview
     id="namedview15"
     pagecolor="#5b5b5b"
     bordercolor="#000000"
     borderopacity="0.25"
     inkscape:showpageshadow="2"
     inkscape:pageopacity="0.0"
     inkscape:pagecheckerboard="true"
     inkscape:deskcolor="#d1d1d1"
     inkscape:zoom="2.7375"
     inkscape:cx="160.54795"
     inkscape:cy="184.29224"
     inkscape:window-width="2560"
     inkscape:window-height="1334"
     inkscape:window-x="-11"
     inkscape:window-y="-11"
     inkscape:window-maximized="1"
     inkscape:current-layer="coordinateSystem" />
  <defs
     id="defs1">
    <marker
       style="overflow:visible"
       id="marker16"
       refX="0"
       refY="0"
       orient="auto-start-reverse"
       inkscape:stockid="Wide arrow"
       markerWidth="1"
       markerHeight="1"
       viewBox="0 0 1 1"
       inkscape:isstock="true"
       inkscape:collect="always"
       preserveAspectRatio="xMidYMid">
      <path
         style="fill:none;stroke:context-stroke;stroke-width:1;stroke-linecap:butt"
         d="M 3,-3 0,0 3,3"
         transform="rotate(180,0.125,0)"
         sodipodi:nodetypes="ccc"
         id="path16" />
    </marker>
    <marker
       style="overflow:visible"
       id="ArrowWide"
       refX="0"
       refY="0"
       orient="auto-start-reverse"
       inkscape:stockid="Wide arrow"
       markerWidth="1"
       markerHeight="1"
       viewBox="0 0 1 1"
       inkscape:isstock="true"
       inkscape:collect="always"
       preserveAspectRatio="xMidYMid">
      <path
         style="fill:none;stroke:context-stroke;stroke-width:1;stroke-linecap:butt"
         d="M 3,-3 0,0 3,3"
         transform="rotate(180,0.125,0)"
         sodipodi:nodetypes="ccc"
         id="path15" />
    </marker>
    <!-- Marker für die Pfeilspitzen, Farbe jetzt per CSS-Variable -->
    <marker
       id="arrow"
       viewBox="0 0 10 10"
       refX="5"
       refY="5"
       markerWidth="3"
       markerHeight="3"
       orient="auto">
      <path
         d="M0,0 L10,5 L0,10 L5,5 Z"
         fill="var(--text-color)"
         id="path1" />
    </marker>
  </defs>
  <!-- Gruppierung mit globalem Styling für das Koordinatensystem -->
  <g
     id="coordinateSystem"
     fill="none"
     stroke="var(--text-color)"
     stroke-width="1"
     style="stroke:#ffffff;stroke-opacity:1">
    <!-- X-Achse: von -35 bis 35, Pfeil am rechten Ende -->
    <line
       x1="-35"
       y1="0"
       x2="35"
       y2="0"
       marker-end="url(#arrow)"
       id="line1"
       style="stroke:#ffffff;stroke-opacity:1;marker-end:url(#marker16);stroke-width:0.5;stroke-dasharray:none" />
    <!-- Y-Achse: Damit positive Y nach oben zeigt, zeichnen wir von unten nach oben -->
    <line
       x1="0"
       y1="35"
       x2="0"
       y2="-35"
       marker-end="url(#arrow)"
       id="line2"
       style="stroke:#ffffff;stroke-opacity:1;fill:#ffffff;fill-opacity:1;marker-end:url(#ArrowWide);stroke-width:0.5;stroke-dasharray:none" />
    <!-- Ticks für die X-Achse (alle 10 Einheiten) -->
    <line
       x1="-30"
       y1="-1"
       x2="-30"
       y2="1"
       id="line3"
       style="stroke:#ffffff;stroke-opacity:1;stroke-width:0.375;stroke-dasharray:none" />
    <line
       x1="-20"
       y1="-1"
       x2="-20"
       y2="1"
       id="line4"
       style="stroke:#ffffff;stroke-opacity:1;stroke-width:0.375;stroke-dasharray:none" />
    <line
       x1="-10"
       y1="-1"
       x2="-10"
       y2="1"
       id="line5"
       style="stroke:#ffffff;stroke-opacity:1;stroke-width:0.375;stroke-dasharray:none" />
    <line
       x1="10"
       y1="-1"
       x2="10"
       y2="1"
       id="line6"
       style="stroke:#ffffff;stroke-opacity:1;stroke-width:0.375;stroke-dasharray:none" />
    <line
       x1="20"
       y1="-1"
       x2="20"
       y2="1"
       id="line7"
       style="stroke:#ffffff;stroke-opacity:1;stroke-width:0.375;stroke-dasharray:none" />
    <line
       x1="30"
       y1="-1"
       x2="30"
       y2="1"
       id="line8"
       style="stroke:#ffffff;stroke-opacity:1;stroke-width:0.375;stroke-dasharray:none" />
    <!-- Ticks für die Y-Achse (alle 10 Einheiten) -->
    <line
       x1="-1"
       y1="-30"
       x2="1"
       y2="-30"
       id="line9"
       style="stroke:#ffffff;stroke-opacity:1;stroke-width:0.375;stroke-dasharray:none" />
    <line
       x1="-1"
       y1="-20"
       x2="1"
       y2="-20"
       id="line10"
       style="stroke:#ffffff;stroke-opacity:1;stroke-width:0.375;stroke-dasharray:none" />
    <line
       x1="-1"
       y1="-10"
       x2="1"
       y2="-10"
       id="line11"
       style="stroke:#ffffff;stroke-opacity:1;stroke-width:0.375;stroke-dasharray:none" />
    <line
       x1="-1"
       y1="10"
       x2="1"
       y2="10"
       id="line12"
       style="stroke:#ffffff;stroke-opacity:1;stroke-width:0.375;stroke-dasharray:none" />
    <line
       x1="-1"
       y1="20"
       x2="1"
       y2="20"
       id="line13"
       style="stroke:#ffffff;stroke-opacity:1;stroke-width:0.375;stroke-dasharray:none" />
    <line
       x1="-1"
       y1="30"
       x2="1"
       y2="30"
       id="line14"
       style="stroke:#ffffff;stroke-opacity:1;stroke-width:0.375;stroke-dasharray:none" />
    <!-- Achsenbeschriftungen mit angepasstem Stroke (kein Stroke) -->
    <text
       xml:space="preserve"
       style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:4.66667px;font-family:Verdana;-inkscape-font-specification:Verdana;text-align:start;writing-mode:lr-tb;direction:ltr;text-anchor:start;fill:#ffffff;fill-opacity:1;stroke:#ffffff;stroke-width:0.25;stroke-opacity:1"
       x="-8.2648401"
       y="-30.730595"
       id="text16"><tspan
         sodipodi:role="line"
         id="tspan16"
         style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:4.66667px;font-family:Verdana;-inkscape-font-specification:Verdana;stroke-width:0.25"
         x="-8.2648401"
         y="-30.730595">Y</tspan></text>
    <text
       xml:space="preserve"
       style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:4.66667px;font-family:Verdana;-inkscape-font-specification:Verdana;text-align:start;writing-mode:lr-tb;direction:ltr;text-anchor:start;fill:#ffffff;fill-opacity:1;stroke:#ffffff;stroke-width:0.25;stroke-opacity:1"
       x="31.004566"
       y="8.3561649"
       id="text17"><tspan
         sodipodi:role="line"
         id="tspan17"
         style="font-size:4.66667px;stroke-width:0.25"
         x="31.004566"
         y="8.3561649">X</tspan></text>
  </g>
</svg>
