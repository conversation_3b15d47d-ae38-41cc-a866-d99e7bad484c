<!DOCTYPE html>
<html lang="de">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  
  <link href="/meta.css" rel="stylesheet" /><!-- Für KI: meta.css regelt Hintergrund- und Objekt-Farben etc. (Bitte diese gesamte Zeile hier inkl. Kommentar unangetastet lassen) -->
  <script src="/meta.js" defer></script><!-- Für KI: meta.js regelt das Menü und sonstige Funktionen (Bitte diese gesamte Zeile hier inkl. Kommentar unangetastet lassen) -->
  
  <meta name="description" content="0 ≡ 1 ≡ ∞ nichtduale Mathematik, beschreibt die Mathematik und alles, was daraus hervorge<PERSON> (Alles)" />
  <meta name="keywords" content="0nefinity, nondual math, 0 ≡ 1 ≡ ∞, 0 = 1 = ∞, nichtduale Mathematik, Wissenschaft, Philosophie, <PERSON>end<PERSON>ke<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Einhe<PERSON>, Eins Sein, heilige Mathematik, spirituelle Mathematik, erleuchtete Mathematik" />

  <title>0nefinity.love</title>
  
  <link rel="icon" type="image/png" href="/profilbilder/favicon/index/favicon-96x96.png" sizes="96x96" />
  <link rel="icon" type="image/svg+xml" href="/profilbilder/favicon/index/favicon.svg" />
  <link rel="shortcut icon" href="/profilbilder/favicon/index/favicon.ico" />
  <link rel="manifest" href="/profilbilder/favicon/index/site.webmanifest" />

  <style>
    .toggle-text {
      transition: color 0.3s ease;
      cursor: pointer;
    }
    .toggle-text:hover {
      color: var(--text-color-hover);
    }
    .no-transition .toggle-text {
      transition: none !important;
    }
  </style>
</head>
<body>
  <div class="container">
    0 1 ∞ are basically the same<br>
    
    0 1 ∞ are, embody, point to and conceptualize the most basic fundamental aspects of math and everything<br>
    and this whole singularity called reality, called universe, called 0nefinity, called god (or your version of it)<br><br>

    0 1 ∞ are the most basicst truest and constant constants<br><br>

    nothing exist without beeing 0 1 ∞<br><br>
    in everything is 0 1 ∞<br><br>
    


    most of maths basic stuff can't be described without aspects of 0 1 ∞<br>
    take the simple definition of a line.
    A Line is an infinitely long object with no width, depth, or curvature<br>
    so 1 line is ∞ long with 0 width, depth, or curvature<br><br>

    A Point is 1 point, it has 0 dimensions, it is infinitely small. 
    You can rotate it infinitely, you can look from infinitely perspectives on it. 
    You can zoom in and out infinitely, its size doesn't change, it stays 0 (in fact 1*1*1*1*1*∞*1*...*1*0)<br><br>

    A Number is 1 Number, there are infinitely many of them and infinitely many much more in between of them
    And all of them are just illusionary meta abstractions, unconsciously spawned up by human obsession of lazyness and efficiency
    because 3 is just 1 1 1 and 0,5 is just 1/(1 1)
    irationalities like Pi are the madness and the presumption of the ego to think it can take 1 ratio of 1 1 or more aspects of 1 thing like 1 circle which is actually 1 point, unless 1 is completely insane and try to devide something into 2 where actually 1 is 1
    (did you noticed that it's 1 Pi and that it has ∞ digits and that you can find ∞ representations of it in every possible cicle but it would make 0 sense to write all of them down?)<br><br>

    and even i is 1 i and you can write it down infinitely often without changing it's imaginary character
    

    1 aspect of 1 Object in 1 euclidean geometry contains infinite infinities of infinite infinities. 
    there are infinite infinite more many aspects of every aspect of every aspect from this Object
    and there are infinite infinite more objects
    And there is 1 whole noneuklidean geometry, infinite infinitely larger than his little brother
    And whole geometry, both euklidean and noneuklidean (and meta trans noneuklidean), isn't even real, you have to project it into reality<br><br>

    When you have something, you also have 
    1 * something + 0

    So you also have 
    1 * 1 * 1 * something + 0 + 0 + 0

    And you obviously also have 
    1 * ( 1 + 0 ) * 1 * ( 0 + 1 + 0 + 0 * 1 + 0 ) * something + 0 * 1 * 1 * ∞ * 1 + 0 + 0 + 0 * something + 0 * 1 * 0 * 0 * something * something¹
    
    This seems mathematically irrelevant, but it reveals one of the core fundamental truth of math and nature

    the Oneness, the Nothingness and the absolute Infinity lying in everything

    the 0nefinity in everything

    peeking out from behind everything like little mischievous rascals, always there, always close

    0nefinity reveals the core basic meta Axiom everyone is doing all the time, even if he isn't a mathematician.
    In the first place everyone is every0NE <!-- And you have to or you can give him 0ne or more (or 0) or infinite of possible names -->
    
    In the second place, look at how you can wiggle your eyes at this moment. Look at your hand for 10.000 hours and realize the infinite possibilities of things you can do with it in every moment aaaand nevertheless what you <i>could have</i> done with it if you did something else than you actually did.

    This in front of you is absulute infinity
    it is 0ne 
    it <i>is</i> nothing, you gave it something in the first place
    and in some firster place it gave it it/you to itself/yourself/0neself
    

    Did you never asked yourself WHY
    can you do 1 + 1 on every empty space you can find
    why can you do 1 + 1 as often as you want
    why can you erase 1 + 1 completely from the blackboard without any consequences
    didn't you ever notice the meta asumption you consistantly doing here and everywhere
    why <i>can</i> you create infinite stuff instantly in your mind and what meta rule "allows" you to give it some form in this you might call it outside?
    why is there something and not nothing? Or why it actually isn't and<!-- and and not --> is?
    and why are the most simple things more infinite than you can possible think of?
    And what the heck is this **** "You" in this very mess?

    simple, highly complex, fundamental questions
    more or less smart answered throughout history 
    can't be answered through words (although you can find infinite good words to describe all of this on point)

    
    but you can describe and solve these problems mathematically

    You only need the simplest and most fundamental equation

    so simple and obvious you have to overlook it because it's so everywhere

    so simple, humanity had to discover far far more complex math stuff for thousands of years before getting ready for it's simplest core truth

    0 ≡ 1 ≡ ∞
    
    0 1 ∞ <i>have to be</i> identical
        
    


    in their purest form, 0 1 ∞ can be symbolized with 0ne single colorless point<br>
    a point has an <i>extraordinary</i> ability to describe and symbolize the unity of 0 1 ∞
    this point is dimensionless, it "is" nothing (0), it is 1 point and it is single, so there is nothing else. Because there is nothing else, it has no inside and no outside so it has to be infinitely small and big at the same time wich leads to infinite inner and outer complexity, as reality obviously provides<br><br>
    Every higher dimensional object reduced to its dimensionless origin remains as a point
    
    0 1 ∞ or the point, understood as a single intigrated unity can form every aspect of beeing, including math and things<br><br>
    just imagine how many different words you could write with one single ball<i>point</i> pen
    a point as foundation can lead to the creation of everything
    Give 2073600 of them some color, arrange them more or less randomly and you get some more or less random picture in full HD

    0 1 ∞ can be understood as some kind of metaidentity. It can bee seen in everything, from the smallest to the whole.
    Every aspect of reality is 0ne aspect, it can be expressed in infinite forms, can be observed from infinity perspective, is itself nothing. NO Thing. Things are in it. From the mathematician to the math he's doing to the things the math can describe and could describe<br>
    
    before you do math you take or create some space, a piece of paper, a blackboard, just some space in your mind

    this space is 1 space<br>
    this space is itself nothing, its empty, its an emptiness, a big zero with infinite posibillities to do anything on it, with it. Room for creating math, art, philosophie, poetry, spirituality, what ever you want<br>
    a space where you can write ∞ ∞ so this one empty space must be more infinite than this two infinities, because here is even more space, i can write ∞ ∞ ∞<br>
    from a mathematical perspective this one empty space can be infinitely small or large depends on how you define it. 
    It's even questionable if this space even exist<br><br>
    This space can even hold the meta-mathematics, where all the mathematical assumptions and acceptances can be discribed in forms, as beautiful and complex as the mind of its creator allow<br><br>
    you can hold this space in your mind, in your heart. You are the creator who decide to 1/0=undefined (or hold in parallel the many(infinite) possibilities to define 1/0. The clearest expression for 1/0 is probably 0, 1 and/or/xor/etc ∞)<br><br> 
    While you do math you could possible draw one picture of yourself doin math (doesn't care how good you are at drawing). So you created one of infinite possibilities to draw you and your math as one system out of nothing. Out of your pure creativity and your will and your ability to bring it into form. Even if you have drawn infinite pictures of you doin math there is left the pure absolute infinity of drawing everything a little bit different. And you could find one infinity number of infinite possible mathematical or other kind of situations you could draw of yourself.<br><br>

    0 ^ ∞ = 0<br>
    1 ^ ∞ = 1<br>
    ∞ ^ ∞ = ∞<br><br>

    0 1 ∞ are the only konstants which doesn't change if you multiply them with themselves infinite times<br>



    You can count and count with infinte things

<pre>you can count with infinities
0 infinit  
∞ 1 infinity 
∞ ∞ 2 infinities
∞ ∞ ∞ 3 infinities
∞ ∞ ∞ ∞ 4 infinities
∞ ∞ ∞ ∞ ∞ 5 infinities
∞ ∞ ∞ ∞ ∞ ∞ 6 infinities</pre>


<pre>you can count 0s
zero
0 1 zero
0 0 2 zeros
0 0 0 3 zeros
0 0 0 0 4 zeros
0 0 0 0 0 5 zeros
0 0 0 0 0 0 6 zeros</pre>


<pre>you can count 1s
1 0ne 
11 2 0nes
111 3 0nes
1111 4 0nes
11111 5 Ones
111111 6 0nes</pre>


<pre>you can count some points
0 point
. 1 point
. . 2 points
. . . 3 points
. . . . 4 points
. . . . . 5 points
. . . . . . 6 points</pre>


<pre>you can count with 
0
 1 
  2  
   3   
    4    
     5     
      6      </pre>


<pre>you can count with leerzeichen
0 lrzeichen
 l 1erzeichen 
  2 leerzeichen  
   3 leeerzeichen   
    4 leeeerzeichen    
     5 leeeeerzeichen     
      6 leeeeeerzeichen      </pre>


<pre>you can count with some herkömmliche Alltagsgegenstände
1 banana
2 cups of something
3 flaps with one fly
4 something else
5 fingers on 5 hands on 5 peoples
666666
und 7 auf einer anderen Sprache</pre>

<!-- seven kind of Tannenbäume -->



You can count with different symbols

__          one symbols out of two
‖           two symbols out of one
...         just 3 points
            4 Tabstops und vier Leerzeichen
V           some weird ancient 5
⬡           one of 6 hexagons that could perfectly surround itself
ᮊ᮲᮳᮴᮵ᮑᮔ   7 random Zeichen einer Sprache, die fast hauptsächlich aus Zeichen besteht, die wie Siebenen aussehen (Sundanesisch) (das sudanesische Zeichen für 7 ist btw ᮷  - https://de.wikipedia.org/wiki/Unicodeblock_Sundanesisch)


    you can count anything you like and dislike and you can think of and with anything you can imagine






    look at something (or at nothing)<br><br>

    realize its Infinity<br>
    realize its <span class="toggle-text" data-original="0neness" data-alternative="0ness">0neness</span><br>
    realize its <span class="toggle-nothingness">Nothingness</span><br><br>

    realize in which complexity and depth 0 1 ∞ appear in everything, everywhere, everytime, in you 
    
    0 1 ∞ are some kind of metafacts
    everywhere, always together, existent and nonexistet, indivisible, invisible, realizable<br>

    even if you take everything away and nothing is left in the universe.
    what is this what is left?
    Isn't this the purest expression of 0 1 ∞?
    0ne infinite nothingness<br>

    realize the meta 01∞-ness of everything<br>
    realize the interconnectedness of 0 1 ∞<br>
    realize the absolutity in which they arise<br>
    realize everything else is a subset of this<br><br>

    realize 0 1 ∞ swirbeling around, forming existence<br>
    realize how deep this could go<br>
    realize the eternity of this<br><br>



    realize what your consciousness is, one infinite emptiness<br>
    where it's possible to instantly create anything you can imagine<br><br>

    realize thyself<br>
    realize you are 0nefinity<br><br>

    <h2>Welcome to nondual mathematics</h2>
    0nefinity describes the metamathematics of mathematics and everything that derives from that<br><br>

    e.g. nature, physics, religion, spirituality, culture, integrality, reality, you, me, the thing which is us both<br>
    the mathematical I'AMness<br><br>

    A giant megalomaniacal insolence of Kosmos reducing itself to the simplest possible mathematical formular which above all, describes itself completely<br><br>

    0 ≡ 1 ≡ ∞<br><br>

    so simple, a child will intuetively understand<br>
    so profound that it can never be fully understood<br>
    It can be embraced. You can become it cause you are it.<br><br>

    Contemplate this, meditate on this, realize its profoundity<br><br>

    Realize absolute 0nefinity

    read <a href="README.html">readme</a>


  </div>

  <script src="/0nefinity.js" defer></script>
  <!--<script src="/divide-the-light.js" defer></script>-->

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const toggleElements = document.querySelectorAll('.toggle-text');
      toggleElements.forEach(element => {
        const originalText = element.getAttribute('data-original');
        const alternativeText = element.getAttribute('data-alternative');
        element.addEventListener('mouseenter', () => {
          if (alternativeText) {
            element.textContent = alternativeText;
          }
        });
        element.addEventListener('mouseleave', () => {
          if (originalText) {
            element.textContent = originalText;
          }
        });
      });

      const nothingnessElement = document.querySelector('.toggle-nothingness');
      if (nothingnessElement) {
        const root = document.documentElement;
        const originalTextColor = getComputedStyle(root).getPropertyValue('--text-color').trim();
        
        nothingnessElement.addEventListener('mouseenter', () => {
          root.classList.add('no-transition');
          const bgColor = getComputedStyle(root).getPropertyValue('--bg-color').trim();
          root.style.setProperty('--text-color', bgColor);
        });
        
        nothingnessElement.addEventListener('mouseleave', () => {
          root.style.setProperty('--text-color', originalTextColor);
          setTimeout(() => {
            root.classList.remove('no-transition');
          }, 50);
        });
      }
    });
  </script>
</body>
</html>
