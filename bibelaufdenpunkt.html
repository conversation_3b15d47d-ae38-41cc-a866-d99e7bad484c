<!DOCTYPE html>
<html lang="de">
<head>
  <meta charset="UTF-8">
  <title>Bibeltext-Visualisierung</title>

  <link href="/meta.css" rel="stylesheet" />
  <script src="/meta.js" defer></script>

  <style>
    body {
      margin: 0;
      overflow: hidden;
    }
    canvas {
      display: block;
    }
  </style>
</head>
<body>
  <canvas id="canvas"></canvas>
  <script>
    const canvas = document.getElementById('canvas');
    const ctx = canvas.getContext('2d');

    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    let textData = '';
    let lines = [];
    let radius = Math.min(canvas.width, canvas.height) / 2 - 50;
    let fontSize = 20;
    let isDragging = false;
    let lastX = 0;
    let lastY = 0;
    let panX = 0;
    let panY = 0;
    let scale = 1;

    fetch('bibel.txt')
      .then(response => response.text())
      .then(data => {
        textData = data.replace(/\s+/g, ' ').trim();
        init();
      })
      .catch(error => console.error(error));

    function init() {
      adjustFontSize();
      generateLines();
      draw();
    }

    function adjustFontSize() {
      ctx.font = `${fontSize}px monospace`;
      let charWidth = ctx.measureText('M').width;
      let lineHeight = fontSize * 1.2;
      let capacity = recalculateCapacity(radius, lineHeight, charWidth);

      while (textData.length > capacity && fontSize > 1) {
        fontSize -= 0.5;
        ctx.font = `${fontSize}px monospace`;
        charWidth = ctx.measureText('M').width;
        lineHeight = fontSize * 1.2;
        capacity = recalculateCapacity(radius, lineHeight, charWidth);
      }
    }

    function generateLines() {
      const lineHeight = fontSize * 1.2;
      const charWidth = ctx.measureText('M').width;
      lines = [];
      let textIndex = 0;

      for (let yPos = -radius + lineHeight / 2; yPos <= radius - lineHeight / 2; yPos += lineHeight) {
        const lineRadius = Math.sqrt(radius * radius - yPos * yPos);
        const maxLineWidth = lineRadius * 2;
        const maxChars = Math.floor(maxLineWidth / charWidth);

        const remainingChars = textData.length - textIndex;
        const charsInLine = Math.min(maxChars, remainingChars);
        const lineText = textData.substr(textIndex, charsInLine);
        textIndex += charsInLine;

        if (lineText.length > 0) {
          lines.push({ text: lineText, y: yPos });
        }
      }
    }

    function recalculateCapacity(radius, lineHeight, charWidth) {
      let capacity = 0;
      for (let yPos = -radius + lineHeight / 2; yPos <= radius - lineHeight / 2; yPos += lineHeight) {
        const lineRadius = Math.sqrt(radius * radius - yPos * yPos);
        const maxLineWidth = lineRadius * 2;
        const maxChars = Math.floor(maxLineWidth / charWidth);
        capacity += maxChars;
      }
      return capacity;
    }

    function draw() {
      ctx.save();
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Dynamische Schriftfarbe aus der CSS-Variable übernehmen
      const textColor = getComputedStyle(document.body).getPropertyValue('--text-color').trim();
      ctx.fillStyle = textColor || 'white'; // Fallback auf 'white', falls die Variable nicht gesetzt ist

      ctx.textBaseline = 'middle';
      ctx.textAlign = 'center';
      ctx.translate(canvas.width / 2 + panX, canvas.height / 2 + panY);
      ctx.scale(scale, scale);

      lines.forEach((line) => {
        ctx.fillText(line.text, 0, line.y);
      });

      ctx.restore();
    }

    canvas.addEventListener('mousedown', (e) => {
      isDragging = true;
      lastX = e.clientX;
      lastY = e.clientY;
    });

    canvas.addEventListener('mousemove', (e) => {
      if (isDragging) {
        const dx = e.clientX - lastX;
        const dy = e.clientY - lastY;
        panX += dx;
        panY += dy;
        lastX = e.clientX;
        lastY = e.clientY;
        draw();
      }
    });

    canvas.addEventListener('mouseup', () => {
      isDragging = false;
    });

    canvas.addEventListener('mouseleave', () => {
      isDragging = false;
    });

    canvas.addEventListener('wheel', (e) => {
      e.preventDefault();
      const zoomIntensity = 0.001;
      scale += e.deltaY * -zoomIntensity;
      scale = Math.min(Math.max(0.05, scale), 10);
      draw();
    });

    window.addEventListener('resize', () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
      radius = Math.min(canvas.width, canvas.height) / 2 - 50;
      init();
    });
  </script>
</body>
</html>
