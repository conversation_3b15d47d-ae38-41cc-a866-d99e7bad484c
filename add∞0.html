<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8" name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">

    <title>Infinity Zeros Game</title>
    <link href="/meta.css" rel="stylesheet" /> 
    <!-- meta.css regelt Hintergrund- und Objekt-Farben etc.. Sonstiges CSS wird häufig nicht benötigt, es sei denn es wird doch benötigt -->
    <script src="/meta.js" defer></script>
    <!-- meta.js regelt das Menü und den Zurückbutton und evt. sonstige zukünftige Metafunktionen -->
    
    <style>
        body {
            margin: 0;
            overflow: hidden;
            position: relative;
            height: 100vh;
            width: 100vw;
            cursor: pointer;
            
            user-select: none;  
        }
        #heading {
            position: absolute;
            top: 15%;
            left: 50%;
            transform: translateX(-50%);
            font-size: 32px;
            z-index: 1;
            text-align: center;
        }
        #center-zero-wrapper {
            position: absolute;
            user-select: none;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(1);
            transition: transform 0.2s ease-in-out;
            will-change: transform;
        }
        #center-zero {
            font-size: 200px;
            pointer-events: none;
        }
        #counters {
            position: absolute;
            top: 65%;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            font-size: 20px;
            white-space: nowrap;
            opacity: 0;
            transition: opacity 0.5s;
        }
        #counters.visible {
            opacity: 1;
        }
        .counter {
            margin: 10px 0;
        }
        .infinite-zero {
            position: absolute;
            font-size: 32px;
            opacity: 1;
            transform: translateY(0);
            animation: fadeOut 1s forwards;
            pointer-events: none;
            will-change: opacity, transform;
        }
        @keyframes fadeOut {
            to {
                opacity: 0;
                transform: translateY(-60px);
            }
        }
    </style>
</head>
<body>

    <div id="heading">Klicke, um unendlich Nullen zu addieren</div>

    <div id="center-zero-wrapper">
        <div id="center-zero">0</div>
    </div>

    <div id="counters">
        <div class="counter" id="count-container">Anzahl aller unendlichfach addierten Nullen: <span id="count">0</span> ∞</div>
        <div class="counter" id="sum-container">Summe aller addierten Nullen: ∞</div>
        <div class="counter" id="total-container">Anzahl der letztendlich vorhandenen Nullen: 1</div>
        <div class="counter" id="value-container">Wert aller Nullen zusammengenommen: 0</div>
    </div>

    <script>
        let count = 0;
        let baseScale = 1; // Basis-Skalierung der Null
        const scaleIncrement = 0.05; // Inkrement pro Klick
        const scaleAnimationDelta = 0.2; // Temporäre Skalierung für die Animation

        const countElement = document.getElementById('count');
        const centerZeroWrapper = document.getElementById('center-zero-wrapper');
        const counters = document.getElementById('counters');
        const sumContainer = document.getElementById('sum-container');
        const totalContainer = document.getElementById('total-container');
        const valueContainer = document.getElementById('value-container');

        // Anfangszustand: Verstecke die zusätzlichen Zähler
        sumContainer.style.display = 'none';
        totalContainer.style.display = 'none';
        valueContainer.style.display = 'none';

        document.addEventListener('click', function(event) {
            count += 1;
            countElement.textContent = count;

            // Anzeige der Zähler nach bestimmten Klicks
            if (count === 10) {
                counters.classList.add('visible');
            }
            if (count === 20) {
                sumContainer.style.display = 'block';
            }
            if (count === 30) {
                totalContainer.style.display = 'block';
            }
            if (count === 40) {
                valueContainer.style.display = 'block';
            }

            // Erzeuge das +∞0 an der Klickposition
            const infZero = document.createElement('div');
            infZero.classList.add('infinite-zero');
            infZero.textContent = '+∞0';
            infZero.style.left = `${event.clientX}px`;
            infZero.style.top = `${event.clientY}px`;
            document.body.appendChild(infZero);

            // Entferne das Element nach der Animation
            infZero.addEventListener('animationend', () => {
                infZero.remove();
            });

            // Animationslogik für die zentrale Null
            // 1. Temporär skalieren
            centerZeroWrapper.style.transform = `translate(-50%, -50%) scale(${baseScale + scaleAnimationDelta})`;

            // 2. Nach der Animation die Basis-Skalierung erhöhen und zurücksetzen
            centerZeroWrapper.addEventListener('transitionend', function handler() {
                // Entferne den Event-Listener, um Mehrfachauslösungen zu vermeiden
                centerZeroWrapper.removeEventListener('transitionend', handler);
                
                // Erhöhe die Basis-Skalierung
                baseScale += scaleIncrement;

                // Setze die Skalierung auf die neue Basis zurück
                centerZeroWrapper.style.transform = `translate(-50%, -50%) scale(${baseScale})`;
            });
        });
    </script>

</body>
</html>
