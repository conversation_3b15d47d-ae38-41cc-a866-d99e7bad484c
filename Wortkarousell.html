<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Text Karussell</title>
<style>
  .karussell {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 20px;
  }
  .item {
    transition: transform 0.9s ease-in-out, font-size 0.9s ease-in-out, opacity 0.9s ease-in-out;
    text-align: center;
  }
  .inactive {
    font-size: 16px;
    opacity: 0.7;
  }
  .moreInactive {
    font-size: 12px;
    opacity: 0.5;
  }
</style>
</head>
<body>
<div>
  <div class="karussell" id="textKarussell">
    <div class="item moreInactive" id="prevItem2"></div>
    <div class="item inactive" id="prevItem1"></div>
    <div class="item" id="activeItem"></div>
    <div class="item inactive" id="nextItem1"></div>
    <div class="item moreInactive" id="nextItem2"></div>
  </div>
</div>

<script>
  const words = ["Paradies", "Elysium", "Arkadien", "Shangri-La", "Zion", "Avalon", "Xanadu", "Nirwana", "Eden"];
  let currentIndex = 0;

  const karussell = document.getElementById('textKarussell');
  const prevItem2 = document.getElementById('prevItem2');
  const prevItem1 = document.getElementById('prevItem1');
  const activeItem = document.getElementById('activeItem');
  const nextItem1 = document.getElementById('nextItem1');
  const nextItem2 = document.getElementById('nextItem2');

  function updateKarussell() {
    activeItem.innerText = words[currentIndex];
    prevItem1.innerText = words[(currentIndex - 1 + words.length) % words.length];
    prevItem2.innerText = words[(currentIndex - 2 + words.length) % words.length];
    nextItem1.innerText = words[(currentIndex + 1) % words.length];
    nextItem2.innerText = words[(currentIndex + 2) % words.length];
  }

  function scroll(direction) {
    currentIndex = (currentIndex + direction + words.length) % words.length;
    updateKarussell();
  }

  updateKarussell();

  karussell.addEventListener('wheel', function(event) {
    if (event.deltaY < 0) scroll(-1);
    else if (event.deltaY > 0) scroll(1);
  });

  let touchStartY = 0;
  karussell.addEventListener('touchstart', function(event) {
    touchStartY = event.touches[0].clientY;
  }, { passive: true });

  karussell.addEventListener('touchmove', function(event) {
    const touchEndY = event.touches[0].clientY;
    if (touchEndY < touchStartY - 10) {
      scroll(1);
      touchStartY = touchEndY;
    } else if (touchEndY > touchStartY + 10) {
      scroll(-1);
      touchStartY = touchEndY;
    }
  }, { passive: true });
</script>
</body>
</html>