<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS Kreise</title>

    <link href="/meta.css" rel="stylesheet" /> 
    <!-- meta.css regelt Hintergrund- und Objekt-Farben etc.. Sonstiges CSS wird häufig nicht benötigt, es sei denn es wird doch benötigt -->
    <script src="/meta.js" defer></script>
    <!-- meta.js regelt das Menü und den Zurückbutton und evt. sonstige zukünftige Metafunktionen -->

    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: black;
        }

        svg {
            width: 100%;
            height: 100%;
        }

        .circle {
            fill: none;
            stroke: var(--text-color);
            stroke-width: 1;
        }
    </style>
</head>
<body>
    <svg id="fractal" viewBox="0 0 300 300"></svg>

    <script>
        function createCircle(svg, cx, cy, r) {
            if (r < 1 || r > 10000) return; // Begrenzung, um eine unendliche Rekursion zu verhindern

            const circle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
            circle.setAttribute("class", "circle");
            circle.setAttribute("cx", cx);
            circle.setAttribute("cy", cy);
            circle.setAttribute("r", r);
            svg.appendChild(circle);

            // Rekursiv kleinere und größere Kreise erstellen
            createCircle(svg, cx, cy, r * 0.9); // Verkleinerung um Faktor 0.7
            createCircle(svg, cx, cy, r * 1.001); // Vergrößerung um Faktor 1.4
        }

        function initializeFractal() {
            const svg = document.getElementById("fractal");
            svg.innerHTML = ''; // Vorherige Kreise entfernen
            createCircle(svg, 150, 150, 1000); // Initialer Kreis
        }

        // Initialisierung des Fraktals
        initializeFractal();
    </script>
</body>
</html>
