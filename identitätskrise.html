<!DOCTYPE html>
<html lang="de">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Zufällige Identitätsoperator-Gleichungen</title>
  <!-- Mathematische Identitätsoperatoren sind ein schönes Beispiel für 0nefinity. 
       Jeder Operator ist 1 Operator. 
       Mathematisch gesehen hat er 0 Einfluss.
       Jeder dieser Operatoren lässt sich ∞-fach anwenden.
       Man munkelt, dass all diese Identitätsoperatoren permanent überall gleichzeitig vorhanden sind. Ein irres Chaos. 
       Zum Glück sind sie im default unsichtbar und man muss sie händisch sichtbar machen.

       Dieses Tool hier soll diese Arbeit erleichtern und generiert eine präferierte Anzahl an Lieblings-Identitätsoperatoren.
       Weiterführend könnte man identitätsoperatoren kreativ anordnen, Bilde<PERSON> malen, etc.
      -->
  
  <link href="/meta.css" rel="stylesheet" />
  <script src="/meta.js" defer></script>
  
  <style>
    html, body {
      margin: 0;
      padding: 0;
      width: 100%;
      height: 100%;
      max-width: 100%;
      background: var(--bg-color);
      color: var(--text-color);
      font-family: Verdana, Geneva, Tahoma, sans-serif;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
      position: relative;
      overflow: hidden;
    }
    #math-container {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-grow: 1;
      width: 100%;
      text-align: center;
      overflow: hidden;
      touch-action: none;
    }
    #formula-math {
      position: absolute;
      left: 50%;
      top: 50%;
      transform-origin: center center;
      /* Wir setzen hier zunächst keine feste Skalierung – diese übernimmt adjustFormulaScale() */
      transform: translate(-50%, -50%) scale(1);
      margin: 0 !important;
      padding: 0 !important;
      line-height: 1;
      font-family: serif;
      color: var(--text-color);
    }
    #math-container * {
      margin: 0;
      padding: 0;
    }
    .controls {
      text-align: center;
      margin: 1em 0;
    }
    label, input, .toggle-button {
      field-sizing: content;
      margin: 0.5em;
      font-size: 1em;
      color: var(--text-color);
      background: var(--bg-color);
      border: 1px solid var(--text-color);
      padding: 0.5em;
      border-radius: 4px;
    }
    .toggle-button {
      cursor: pointer;
    }
    /* Container für Toggle-Schaltfläche und Liste */
    .operator-container {
      position: relative;
      display: inline-block;
    }
    /* Operatorenliste direkt unterhalb der Toggle-Schaltfläche */
    .operator-list {
      display: none;
      position: absolute;
      top: 100%; /* direkt unterhalb der Schaltfläche */
      left: 0;
      width: 120px; /* feste Breite, damit alle Einträge gleich groß sind */
      background: var(--bg-color);
      border: 1px solid var(--text-color);
      padding: 0.5em;
      max-height: 70vh;
      overflow-y: auto;
      z-index: 100;
      box-shadow: 0 0 10px rgba(0,0,0,0.5);
    }
    /* Einheitliches Layout für die einzelnen Operator-Einträge */
    .operator-item {
      display: flex;
      align-items: center;
    }
    .operator-item input[type="checkbox"] {
      width: 1.2em;
      height: 1.2em;
      margin-right: 0.5em;
      flex-shrink: 0;
    }
    .operator-item label {
      flex-grow: 1;
    }
    #math-container, #formula-math {
      box-sizing: border-box !important;
    }
    /* Wir entfernen die feste font-size, damit die Skalierung über CSS nicht interferiert */
    #formula-math {
      font-size: initial;
    }
  </style>
</head>
<body>
  
  <div class="controls">
    <label for="operatorCount">Anzahl der Operatoren:</label>
    <input type="number" id="operatorCount" value="1" min="0" />
    <div class="operator-container">
      <button class="toggle-button" id="toggleListBtn">Operatoren</button>
      <div class="operator-list" id="operatorList">
        <!-- Checkboxen werden hier eingefügt -->
      </div>
    </div>
  </div>
  
  <h2>nothing happens here</h2>
  <div id="math-container">
    <math xmlns="http://www.w3.org/1998/Math/MathML" display="block" id="formula-math">
      <mi id="center-point">irgendwas</mi>
    </math>
  </div>
  
  <script>
    let allOperators = [];
    let selectedOperators = [];
    let userZoomFactor = 1; 

    // Funktion zum Parsen der URL-Parameter
    function parseURLParams() {
      // Ausschließlich kompaktes URL-Format: ?ANZAHL*OPERATOR1,OPERATOR2,...
      const url = window.location.href;
      let count = 1; // Standardwert
      const activeOps = []; // Leere Liste für aktivierte Operatoren
      
      // Kompaktes Format: ?ANZAHL*OPERATOR1,OPERATOR2,...
      const compactMatch = url.match(/\?(\d+)\*(.*?)(?=$|&)/);
      
      if (compactMatch) {
        // Anzahl der Operatoren
        const parsedCount = parseInt(compactMatch[1]);
        if (!isNaN(parsedCount) && parsedCount >= 0) {
          count = parsedCount;
        }
        
        // Operatoren
        const opsRaw = compactMatch[2];
        if (opsRaw) {
          opsRaw.split(',').forEach(op => {
            // Dekodiere jeden Operator ohne spezielle URL-Kodierung
            const decodedOp = op;
            activeOps.push(decodedOp);
          });
        }
      }
      
      console.log("Anzahl:", count, "Aktive Operatoren:", activeOps);
      return { count, activeOps };
    }
    
    // Funktion zum Aktualisieren der URL entsprechend dem aktuellen Zustand
    function updateURL() {
      const count = parseInt(document.getElementById('operatorCount').value) || 0;
      const activeOps = selectedOperators.map(op => op.name);
      
      // Wir erstellen die kompakte URL: ?ANZAHL*OPERATOR1,OPERATOR2,...
      let newUrl = window.location.pathname + '?' + count + '*';
      
      if (activeOps.length > 0) {
        // Erzeuge eine Liste mit speziell formatierten Operatoren
        const formattedOps = activeOps.map(op => {
          // Spezielle Normalisierungen direkt beim Namen vornehmen
          if (op === '¹') return '^1';  // ¹ durch ^1 ersetzen
          if (op.includes('√')) return op.replace(/√/g, 'sqrt');
          return op;
        });
        
        // Direktes Zusammenfügen ohne weitere Kodierung
        newUrl += formattedOps.join(',');
      }
      
      // Aktualisiere die URL
      window.history.pushState({}, '', newUrl);
      console.log("Neue URL:", newUrl);
    }

    async function loadOperators() {
      try {
        const response = await fetch('/identities.json');
        if (!response.ok) throw new Error(`HTTP-Fehler! Status: ${response.status}`);
        const data = await response.json();
        
        // Debug: Die geladenen Operatoren ausgeben
        console.log("Geladene Operatoren:", data.identitätsoperatoren);
        
        return data.identitätsoperatoren;
      } catch (error) {
        console.error('Fehler beim Laden der Operatoren:', error);
        return [];
      }
    }
  
    // KOMPLETT ÜBERARBEITETE FUNKTION
    function generateMathMLFormula(operators, operatorCount) {
      // Beginne mit dem Startelement
      let currentFormula = `<mi id="center-point">irgendwas</mi>`;
      
      // Array von zu wählenden Operatoren erstellen
      const operatorsToUse = [];
      for (let i = 0; i < operatorCount; i++) {
        if (operators.length === 0) break;
        // Zufälligen Operator auswählen
        const randomIndex = Math.floor(Math.random() * operators.length);
        operatorsToUse.push(operators[randomIndex]);
      }
      
      // Operatoren von hinten nach vorne verarbeiten für korrekte Verschachtelung
      for (let i = operatorsToUse.length - 1; i >= 0; i--) {
        const operator = operatorsToUse[i];
        
        // Spezielle Behandlung für Wurzeloperator
        if (operator.name === "√1") {
          // Wurzeln mit dem Index 1 erstellen, ähnlich wie im guten Beispiel
          currentFormula = `<mroot>${currentFormula}<mn>1</mn></mroot>`;
        } else {
          // Standard-Ersetzung für andere Operatoren
          currentFormula = operator.mathml.replace('<mi>x</mi>', currentFormula);
        }
      }
      
      return currentFormula;
    }
  
    // Diese Funktion passt die Skalierung der Formel dynamisch an den verfügbaren Platz an
    function adjustFormulaScale() {
      const container = document.getElementById('math-container');
      const formula = document.getElementById('formula-math');
      
      // Setze die Skalierung vorübergehend auf 1, um die "intrinsische" Größe der Formel zu ermitteln
      formula.style.transform = 'translate(-50%, -50%) scale(1)';
      
      const containerRect = container.getBoundingClientRect();
      const formulaRect = formula.getBoundingClientRect();
      
      // Berechne die Skalierungsfaktoren in x- und y-Richtung
      const scaleX = containerRect.width / formulaRect.width;
      const scaleY = containerRect.height / formulaRect.height;
      
      // Wähle den kleineren Faktor, damit die Formel komplett in den Container passt
      // Multipliziere mit 0.9, um einen kleinen Rand zu lassen
      const baseScale = Math.min(scaleX, scaleY) * 0.9;
      
      // Die endgültige Skalierung ist der Basiswert multipliziert mit der Benutzeranpassung
      const newScale = baseScale * userZoomFactor;
      formula.style.transform = `translate(-50%, -50%) scale(${newScale})`;
    }
  
    function updateFormula(mathMLContent) {
      const container = document.getElementById('formula-math');
      container.innerHTML = mathMLContent;
      
      // Nach dem Rendern die Größe anpassen
      adjustFormulaScale();
    }
  
    function refreshFormula() {
      const count = parseInt(document.getElementById('operatorCount').value) || 0;
      const mathMLContent = generateMathMLFormula(selectedOperators, count);
      updateFormula(mathMLContent);
      // URL aktualisieren
      updateURL();
    }
  
    function createOperatorList(operators) {
      const listContainer = document.getElementById('operatorList');
      listContainer.innerHTML = '';
      
      // URL-Parameter auslesen
      const { activeOps } = parseURLParams();
      const useSpecificOps = activeOps.length > 0;
      
      // Debug-Ausgabe mit allen Details
      console.log("Verfügbare Operatoren mit Details:", operators);
      console.log("Aktive Operatoren aus URL:", activeOps);
  
      operators.forEach(op => {
        const item = document.createElement('div');
        item.className = 'operator-item';
  
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.id = `op-${op.name}`;
        
        // Checkbox-Status basierend auf URL-Parametern setzen
        if (useSpecificOps) {
          // Direkte Prüfung ohne Normalisierung
          checkbox.checked = activeOps.includes(op.name);
          
          // Spezielle Prüfung für ^1
          if (!checkbox.checked && op.name === '¹' && activeOps.includes('^1')) {
            checkbox.checked = true;
            console.log("Spezialfall: ^1 in URL entspricht ¹ im Operator");
          }
          
          console.log(`Operator: ${op.name}, Aktiv: ${checkbox.checked}`);
        } else {
          checkbox.checked = true; // Standardmäßig alle aktivieren, wenn keine Parameter vorhanden sind
        }
        
        checkbox.addEventListener('change', () => {
          selectedOperators = operators.filter(o =>
            document.getElementById(`op-${o.name}`).checked
          );
          refreshFormula();
        });
  
        const label = document.createElement('label');
        label.htmlFor = `op-${op.name}`;
        label.textContent = op.name;
  
        item.appendChild(checkbox);
        item.appendChild(label);
        listContainer.appendChild(item);
      });
  
      // Setze die ausgewählten Operatoren basierend auf den Checkbox-Zuständen
      selectedOperators = operators.filter(op => 
        document.getElementById(`op-${op.name}`).checked
      );
    }
  
    // Benutzer-Zoom per Mausrad
    function onWheelZoom(event) {
      event.preventDefault();
      const zoomSensitivity = 0.001;
      userZoomFactor += event.deltaY * -zoomSensitivity;
      userZoomFactor = Math.min(Math.max(userZoomFactor, 0.1), 10);
      adjustFormulaScale();
    }
  
    window.addEventListener('DOMContentLoaded', async () => {
      // URL-Parameter auslesen
      const { count, activeOps } = parseURLParams();
      
      // Operatoren laden
      allOperators = await loadOperators();
      
      // Operatorliste erstellen (wird anhand von URL-Parametern initialisiert)
      createOperatorList(allOperators);
      
      // Anzahl der Operatoren gemäß URL-Parameter setzen
      document.getElementById('operatorCount').value = count;
  
      document.getElementById('operatorCount').addEventListener('input', refreshFormula);
  
      const toggleBtn = document.getElementById('toggleListBtn');
      const operatorList = document.getElementById('operatorList');
      const operatorContainer = document.querySelector('.operator-container');
      
      // Toggle-Button Event
      toggleBtn.addEventListener('click', (event) => {
        operatorList.style.display = (operatorList.style.display === 'none' || operatorList.style.display === '') 
          ? 'block' 
          : 'none';
        event.stopPropagation(); // Verhindert, dass das Klick-Event zum Dokument weitergeleitet wird
      });
      
      // Event-Listener für Klicks auf das Dokument hinzufügen
      document.addEventListener('click', (event) => {
        // Prüfen, ob der Klick außerhalb des Operator-Containers war
        if (!operatorContainer.contains(event.target)) {
          // Wenn ja, die Liste schließen
          operatorList.style.display = 'none';
        }
      });
      
      // Verhindern, dass Klicks innerhalb der Liste die Liste schließen
      operatorList.addEventListener('click', (event) => {
        event.stopPropagation();
      });
  
      document.getElementById('math-container').addEventListener('wheel', onWheelZoom, { passive: false });
  
      // Bei Fenstergrößenänderungen die Formel neu skalieren
      window.addEventListener('resize', adjustFormulaScale);
  
      refreshFormula();
    });
  </script>
  
</body>
</html>